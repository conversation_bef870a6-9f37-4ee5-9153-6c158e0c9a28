<template>
  <ElDialog
    v-model="dialogVisible"
    :destroy-on-close="true"
    :title="title"
    @close="dialogClosed"
    top="2%"
    :style="{ width: '80%' }"
  >
    <TransferSelector
      v-model:selection-data="selectionData"
      :choice-class-data="choiceClassData"
      :choice-detail-data="choiceDetailData"
      :cur-tab="curTab"
      @select="classSelect"
      @class-search="classSearch"
      @detail-search="detailSearch"
    />

    <template #footer>
      <ElButton @click="dialogClosed">取消</ElButton>
      <ElButton type="primary" @click="submit">确定</ElButton>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import type { MaterialSearchTypeEnum } from '#/api/enterpriseCenter/materialEntryCheck/materialEntryCheck';
import type { addContractCompilationType } from '#/api/enterpriseCenter/materialManagement/materialContract';

import { inject, onBeforeMount, ref, watch } from 'vue';

import { ElButton, ElDialog, ElMessage } from 'element-plus';

import {
  addInspectionDetail,
  getMaterialCategoryList,
  getMaterialDetailList,
} from '#/api/enterpriseCenter/materialEntryCheck/materialEntryCheck';
import TransferSelector from '#/components/TransferSelector/index.vue';

export interface addOrEditFormType extends addContractCompilationType {
  id?: null | string;
}

const props = withDefaults(
  defineProps<{
    infoData: {
      inspectionBillId: string;
      materialSearchType?: string;
      purchaseType: string;
    };
    title: string;
    visible: boolean;
  }>(),
  {
    title: '',
    visible: false,
    infoData: () => {
      return {
        inspectionBillId: '',
        materialSearchType: '',
        purchaseType: '',
      };
    },
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

// 当前选择的数据
const curTab = inject<any>('curTab');

// 选择区的分类数据
const choiceClassData = ref<any>([]);
// 选择区的明细数据
const choiceDetailData = ref<any>([]);
// 确认区的数据
const selectionData = inject<any>('goodList');
// 传递的表单
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  (nval) => {
    localInfoData.value = nval;
    // 每次传值的时候调用分类列表
    getCategoryList();
  },
  { deep: true, immediate: true },
);
// 弹窗是否展示
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    dialogVisible.value = nval;
  },
  { deep: true, immediate: true },
);
// 获取分类列表
async function getCategoryList(name = '') {
  const params = {
    inspectionBillId: localInfoData.value.inspectionBillId,
    materialSearchType: localInfoData.value
      .materialSearchType as MaterialSearchTypeEnum,
    purchaseType: localInfoData.value.purchaseType,
    name,
  };
  const res = await getMaterialCategoryList(params);
  choiceClassData.value = res || [];
}
function classSearch(text: string) {
  getCategoryList(text);
}

async function classSelect(row: any) {
  getDetailList(row.id);
}
// 获取明细列表
async function getDetailList(materialCategoryId: string, name: string = '') {
  const params = {
    inspectionBillId: localInfoData.value.inspectionBillId,
    materialSearchType: localInfoData.value
      .materialSearchType as MaterialSearchTypeEnum,
    materialCategoryId,
    name,
  };

  const ids = new Set(selectionData.value.map((v: any) => v.id));
  const res = await getMaterialDetailList(params);

  const data = res.map((item: any) => {
    const selected = !!ids.has(item.id);
    return {
      ...item,
      specificationModel: item.spec,
      meteringUnit: item.unit,
      selected,
    };
  });
  choiceDetailData.value = res ? data : [];
}
function detailSearch(text: string, row: any) {
  getDetailList(row.id, text);
}

// 关闭弹窗
function dialogClosed() {
  emit('update:visible', false);
}

// 提交
const submit = async () => {
  const filterSelectionData = selectionData.value.filter(
    (v: any) => !v.disabled,
  );
  const data = filterSelectionData.map((item: any) => {
    return {
      materialId: item.id,
      materialName: item.name,
      materialSpec: item.spec,
      unit: item.unit,
    };
  });

  if (data.length <= 0) {
    ElMessage.warning('请先添加材料');
    return;
  }
  const inspectionBillId = localInfoData.value.inspectionBillId;

  const res = await addInspectionDetail(inspectionBillId, data);

  if (res) {
    ElMessage.success('添加成功');
    emit('refresh');
    emit('update:visible', false);
  }
};
// 初始化
async function init() {}

onBeforeMount(async () => {
  init();
});
</script>
<style scoped lang="scss"></style>
