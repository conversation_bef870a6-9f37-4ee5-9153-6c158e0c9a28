<template>
  <div class="edit-drawer">
    <ElDrawer
      v-bind="$attrs"
      v-model="drawerVisible"
      :modal="false"
      :destroy-on-close="true"
      :append-to-body="true"
      :size="sizeNum"
      modal-class="pointer-events-none"
      class="pointer-events-auto"
      :with-header="false"
      @close="handleClose"
      @opened="handleOpen"
    >
      <div class="header box-border w-full pl-4 pr-4">
        <div class="flex h-[60px] w-full items-center justify-between">
          <div class="header-left flex items-center justify-between"></div>
          <div class="header-right flex">
            <div class="btn-group ml-4 pr-4">
              <ElButton type="primary" size="default" @click="insureSubmit">
                {{
                  localInfoData.submitStatus === SubmitStatus.PENDING
                    ? '提交'
                    : '取消提交'
                }}
              </ElButton>
              <ElButton type="default" size="default"> 导出单据 </ElButton>
            </div>
            <div class="flex">
              <IconifyIcon
                @click="prevBtn"
                class="icon-box mr-4 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                icon="majesticons:chevron-left-line"
              />
              <IconifyIcon
                @click="nextBtn"
                class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                icon="majesticons:chevron-right-line"
              />
            </div>
            <div>
              <ElTooltip
                :content="isFullScreen ? '收起' : '全屏'"
                placement="bottom"
              >
                <IconifyIcon
                  @click="isFullScreen = !isFullScreen"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  :icon="
                    isFullScreen
                      ? 'majesticons:arrows-collapse-full'
                      : 'majesticons:arrows-expand-full-line'
                  "
                />
              </ElTooltip>
            </div>
            <div>
              <IconifyIcon
                @click="closeClick"
                class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                icon="ep:close"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="content relative h-[calc(100%-60px)] p-[24px] pb-0">
        <div class="header relative flex h-[80px] items-center justify-center">
          <div class="title flex text-[24px] font-bold">
            物资进场验收记录 (
            <div class="text-orange-500">审核中</div>
            )
          </div>
          <div class="qrcode absolute right-20 flex items-center">
            <QrcodeVue value="物资进场验收记录" :size="100" />
            <div class="ml-4 text-sm">导出{{ 2 }}次</div>
          </div>
        </div>

        <div class="content h-[calc(100%-180px)] w-full">
          <div class="top-form pb-4 pt-8">
            <ElForm :model="formData" class="grid grid-cols-3 gap-x-20 gap-y-5">
              <ElFormItem
                label="项目名称："
                label-width="100px"
                size="large"
                label-position="left"
              >
                <ElInput
                  size="large"
                  v-model="formData.orgName"
                  placeholder=""
                  disabled
                />
              </ElFormItem>
              <ElFormItem
                label="采购类型："
                label-width="100px"
                size="large"
                label-position="left"
              >
                <ElSelect
                  size="large"
                  v-model="formData.purchaseType"
                  placeholder=""
                  @change="purchaseypeChange"
                >
                  <ElOption
                    v-for="v in purchaseTypeOptions"
                    :key="v.value"
                    :label="v.label"
                    :value="v.value"
                  />
                </ElSelect>
              </ElFormItem>
              <ElFormItem
                label="单据编码："
                label-width="100px"
                size="large"
                label-position="left"
              >
                <ElInput
                  size="large"
                  v-model="formData.code"
                  placeholder=""
                  disabled
                />
              </ElFormItem>
              <ElFormItem
                label="供应商名称："
                label-width="100px"
                size="large"
                label-position="left"
              >
                <ElSelect
                  size="large"
                  v-model="formData.supplierId"
                  placeholder=""
                  @change="supplierChange"
                  clearable
                >
                  <ElOption
                    v-for="v in supplierOptions"
                    :key="v.value"
                    :label="v.label"
                    :value="v.value"
                  />
                </ElSelect>
              </ElFormItem>
              <ElFormItem
                label="合同名称："
                label-width="100px"
                size="large"
                label-position="left"
              >
                <ElSelect
                  size="large"
                  v-model="formData.contractId"
                  placeholder=""
                  clearable
                  :disabled="
                    formData.purchaseType === PurchaseType.PARTY_A_SUPPLIED ||
                    formData.purchaseType === PurchaseType.TRANSFER_IN
                  "
                  @change="contractChange"
                >
                  <ElOption
                    v-for="v in contractOptions"
                    :key="v.value"
                    :label="v.label"
                    :value="v.value"
                  />
                </ElSelect>
              </ElFormItem>
              <ElFormItem
                label="进场时间："
                label-width="100px"
                size="large"
                label-position="left"
              >
                <ElDatePicker
                  size="large"
                  v-model="formData.entryDate"
                  type="date"
                  placeholder=""
                  clearable
                  disabled
                />
              </ElFormItem>
            </ElForm>
          </div>
          <div class="table h-[400px] w-full">
            <div class="table h-full w-full overflow-hidden">
              <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
                <template #seq="{ row, $rowIndex }">
                  <div v-if="row.id">{{ $rowIndex + 1 }}</div>
                </template>
                <template #materialName="{ row, $rowIndex }">
                  <div class="flex items-center justify-center">
                    <div class="flex">
                      <div>{{ row.materialName }}</div>
                      <div
                        v-if="
                          ($rowIndex === 1 && tableOptions.data.length <= 0) ||
                          $rowIndex === tableOptions.data.length - 1
                        "
                      >
                        <ElButton
                          size="small"
                          @click="transferDataClick({ row })"
                        >
                          +
                        </ElButton>
                      </div>
                    </div>
                  </div>
                </template>
              </VxeGrid>
            </div>
          </div>
          <div class="bottom-form pt-8">
            <ElForm class="grid grid-cols-3 gap-x-20 gap-y-5">
              <ElFormItem
                label="材料员："
                label-width="100px"
                size="large"
                label-position="left"
              >
                <ElInput size="large" v-model="formInfo.user" disabled />
              </ElFormItem>
              <ElFormItem
                label="施工员："
                label-width="100px"
                size="large"
                label-position="left"
              >
                <ElInput size="large" v-model="formInfo.user" disabled />
              </ElFormItem>
              <ElFormItem
                label="分包材料员："
                label-width="100px"
                size="large"
                label-position="left"
              >
                <ElInput size="large" v-model="formInfo.user" disabled />
              </ElFormItem>
              <ElFormItem
                label="编制人："
                label-width="100px"
                size="large"
                label-position="left"
              >
                <ElInput size="large" v-model="formInfo.creator" disabled />
              </ElFormItem>
            </ElForm>
          </div>
        </div>

        <div class="footer flex h-[100px] items-center">
          <!-- <ElButton type="primary" size="default" @click="insureAduit">
            发起审核
          </ElButton> -->
        </div>
      </div>
    </ElDrawer>

    <AddOrEditMaterial
      v-model:visible="addOrEditMaterialVisible"
      :info-data="addMaterialinfoData"
      title="选择材料"
    />
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  nextTick,
  onBeforeMount,
  provide,
  reactive,
  ref,
  watch,
} from 'vue';

import { IconifyIcon } from '@vben/icons';

import {
  dayjs,
  ElButton,
  ElDatePicker,
  ElDrawer,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElTooltip,
} from 'element-plus';
import QrcodeVue from 'qrcode.vue';

import {
  delInspectionDetail,
  editInspectionBill,
  editInspectionDetail,
  getInspectionDetailList,
  getSupplierAndContractlList,
} from '#/api/enterpriseCenter/materialEntryCheck/materialEntryCheck';
import { setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

import AddOrEditMaterial from './AddOrEditMaterial.vue';

defineOptions({
  name: 'MaterialEntryCheckEditDrawer',
});
const props = withDefaults(
  defineProps<{
    editable?: boolean;
    infoData: any;
    visible: boolean;
  }>(),
  {
    editable: true,
    visible: false,
    infoData: {},
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'move', payload: any): void;
}>();

// 提交状态
const SubmitStatus = {
  PENDING: 'PENDING', // 未提交
  SUBMITTED: 'SUBMITTED', // 已提交
} as const;
// 采购类型
const PurchaseType = {
  SELF_PURCHASE: 'SELF_PURCHASE', // 自采
  CENTRALIZED_PURCHASE: 'CENTRALIZED_PURCHASE', // 集采
  PARTY_A_DIRECTED: 'PARTY_A_DIRECTED', // 甲指
  PARTY_A_SUPPLIED: 'PARTY_A_SUPPLIED', // 甲供
  TRANSFER_IN: 'TRANSFER_IN', // 调拨
} as const;
type PurchaseTypeEnum = (typeof PurchaseType)[keyof typeof PurchaseType];
// 采购类型选项
const purchaseTypeOptions = [
  {
    label: '自采',
    value: PurchaseType.SELF_PURCHASE,
  },
  {
    label: '集采',
    value: PurchaseType.CENTRALIZED_PURCHASE,
  },
  {
    label: '甲指',
    value: PurchaseType.PARTY_A_DIRECTED,
  },
  {
    label: '甲供',
    value: PurchaseType.PARTY_A_SUPPLIED,
  },
  {
    label: '调拨',
    value: PurchaseType.TRANSFER_IN,
  },
];

// 供应商和合同名称的总数据
const contractAndSupplierOptions = ref<any>([]);
// 供应商选项
const supplierOptions = ref<any>([]);
// 合同选项
const contractOptions = ref<any>([]);

// 是否展示弹窗
const drawerVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
const curTab = ref();
provide('curTab', curTab);
// 表单数据
const formData = ref({
  id: '',
  orgName: '',
  purchaseType: '',
  code: '',
  supplierId: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  entryDate: '',
});

// 每次表单数据改变的时候就调用修改接口

// 底部展示数据
const formInfo = ref({
  user: '',
  creator: '',
});
// 全部的外层数据
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  async (nval) => {
    localInfoData.value = nval;

    const {
      id,
      orgName,
      purchaseType,
      code,
      supplierName,
      contractName,
      year,
      day,
      month,
      supplierId,
      contractId,
      creator,
    } = nval;

    formData.value = {
      id,
      orgName,
      supplierId,
      contractId,
      purchaseType, // 默认为自采
      code,
      supplierName,
      contractName,
      entryDate: dayjs(`${year}/${month}/${day}`).format('YYYY-MM-DD'),
    };
    formInfo.value = {
      user: '',
      creator,
    };

    // 调用获取供应商名称
    await getSupplierContractlList();

    // 根据供应商Id 给 合同名称Id 赋值

    if (supplierId) {
      const data = contractAndSupplierOptions.value.find(
        (item: any) => item.id === formData.value.supplierId,
      );

      contractOptions.value = data?.contracts
        ? data.contracts.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          })
        : [];
    }
  },
);

// 采购类型改变
const purchaseypeChange = async () => {
  formData.value.supplierId = '';
  formData.value.supplierName = '';

  formData.value.contractId = '';
  formData.value.contractName = '';

  supplierOptions.value = [];
  contractOptions.value = [];

  await getSupplierContractlList();

  if (supplierOptions.value.length === 1) {
    formData.value.supplierId = supplierOptions.value[0].value;
    formData.value.supplierName = supplierOptions.value[0].label;
  } else {
    formData.value.supplierId = '';
    formData.value.supplierName = '';
  }

  await insureSave();
};
// 是否全屏
const isFullScreen = ref(false);
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '74%';
});

const addOrEditMaterialVisible = ref(false);

const addMaterialinfoData = ref();

// 表格配置
const emptyGoodsItem = {
  id: '',
  name: '',
  editable: false,
  disabled: false,
};

const tableRef = ref();
const currentItem = ref();
const unitOptions = ref([]);
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    width: '150',
    slots: {
      default: 'materialName',
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '200',
  },
  {
    field: 'qualityStandard',
    title: '质量标准',
    width: '200',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入质量标准',
      },
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '200',
    editRender: {
      name: 'VxeSelect',
      options: unitOptions,
    },
  },
  {
    field: 'siteEntryQuantity',
    title: '进场数量',
    width: '200',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入进场数量',
      },
    },
  },
  {
    field: 'actualQuantity',
    title: '实收数量',
    width: '150',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入实收数量',
      },
    },
  },
  {
    field: 'appearanceDescription',
    title: '外观质量描述',
    width: '150',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入外观质量描述',
      },
    },
    // slots: {
    //   default: 'createAt',
    // },
  },
  {
    field: 'remark',
    title: '备注',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入外观质量描述',
      },
    },
    // minWidth: '150',
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  loading: false,
  cellClassName: ({ row, column }: any) => {
    return column.editRender || !row.id ? '' : 'bg-gray-100';
  },
  height: '100%',
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'MOVE_UP',
            name: '上移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          },
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options }: any) => {
      options[0].forEach((item: any) => {
        switch (item.code) {
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      // 版本数据已启用无法进行编辑
      if (row.submitStatus === SubmitStatus.PENDING) {
        ElMessage.warning('请先提交数据');
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
});
// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
    const optionalUnitArr = row?.optionalUnit
      ? row.optionalUnit.split(',')
      : [];
    const options = optionalUnitArr.map((item: any) => {
      return {
        label: item,
        value: item,
      };
    });

    unitOptions.value = options;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem);
  },
  async menuClick({ menu, row }: { menu: any; row: any }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delInspectionDetail(id);
        if (res) {
          refreshData();
          ElMessage.success('删除成功');
        }
      });
    }
  },
  // 完成编辑
  async editClosed({ row }: any) {
    const data = {
      id: row.row,
      qualityStandard: row.qualityStandard,
      unit: row.unit,
      siteEntryQuantity: row.siteEntryQuantity,
      actualQuantity: row.actualQuantity,
      appearanceDescription: row.appearanceDescription,
      orderNo: row.orderNo,
      remark: row.remark,
    };
    const res = await editInspectionDetail(data);
    if (res) {
      refreshData();
      ElMessage.success(`修改成功`);
    }
  },
};
// 刷新数据
async function refreshData() {
  await getList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem);
}

// 确认提交
async function insureSubmit() {
  const {
    id,
    purchaseType,
    entryDate,
    supplierId,
    supplierName,
    contractId,
    contractName,
  } = formData.value;

  const year = Number(dayjs(entryDate).format('YYYY'));
  const month = Number(dayjs(entryDate).format('M'));
  const day = Number(dayjs(entryDate).format('D'));

  const data = {
    id,
    purchaseType,
    supplierId,
    supplierName,
    contractId,
    contractName,
    // auditStatus: AuditStatusEnum;
    submitStatus: SubmitStatus.SUBMITTED, // 修改提交状态为已提交
    year,
    month,
    day,
  };

  const res = await editInspectionBill(data);
  if (res) {
    ElMessage.success(`提交成功`);
    emit('update:visible', false);
    emit('refresh');
  }
}

// 确认修改
async function insureSave() {
  const year = Number(dayjs(formData.value.entryDate).format('YYYY'));
  const month = Number(dayjs(formData.value.entryDate).format('M'));
  const day = Number(dayjs(formData.value.entryDate).format('D'));
  const params = {
    id: formData.value.id,
    purchaseType: formData.value.purchaseType,
    supplierId: formData.value.supplierId,
    supplierName: formData.value.supplierName,
    contractId: formData.value.contractId,
    contractName: formData.value.contractName,

    year,
    month,
    day,
  };

  await editInspectionBill(params);
  emit('refresh');
}

// 供应商改变
async function supplierChange() {
  const data = contractAndSupplierOptions.value.find(
    (item: any) => item.id === formData.value.supplierId,
  );
  formData.value.supplierName = data.name;
  contractOptions.value = data?.contracts
    ? data.contracts.map((item: any) => {
        return {
          label: item.name,
          value: item.id,
        };
      })
    : [];

  if (contractOptions.value.length === 1) {
    formData.value.contractId = contractOptions.value[0].value;
    formData.value.contractName = contractOptions.value[0].label;
  } else {
    formData.value.contractId = '';
    formData.value.contractName = '';
  }

  await insureSave();
}

// 合同改变
async function contractChange() {
  const data = contractOptions.value.find(
    (item: any) => item.value === formData.value.contractId,
  );

  formData.value.contractName = data.label;

  await insureSave();
}

// 弹窗打开回调
async function handleOpen() {
  await getList();
  nextTick(() => {
    tableRef.value?.recalculate(); // 重新计算表格尺寸
  });
}

// 弹窗关闭的回调
function handleClose() {
  emit('update:visible', false);
}
const goodList = ref([]);
provide('goodList', goodList);
// 获取表格数据
async function getList() {
  const { id } = localInfoData.value;

  const res = await getInspectionDetailList(id);

  tableOptions.data =
    res.length === 0 ? [emptyGoodsItem] : [...res, emptyGoodsItem];
  goodList.value = res;
  // tableOptions.loading = false;
}

// 点击穿梭框
async function transferDataClick({ row }: any) {
  // 在这里调用获取材料的接口
  // if (!editable.value) {
  //   ElMessage.warning('当前数据已提交,不可编辑');
  //   return false;
  // }
  const isPARTY_A_SUPPLIED =
    formData.value.purchaseType === PurchaseType.PARTY_A_SUPPLIED;
  const isTRANSFER_IN =
    formData.value.purchaseType === PurchaseType.TRANSFER_IN;
  const isLXSUPPLIED = formData.value.supplierName === '零星材料供应商';

  // 甲供 调拨 还有 甲指 中的零星供应商 是 选择材料字典 否则是合同材料
  curTab.value =
    isPARTY_A_SUPPLIED || isTRANSFER_IN || isLXSUPPLIED
      ? 'MATERIAL_DICT'
      : 'CONTRACT';

  addMaterialinfoData.value = {
    inspectionBillId: formData.value.id,
    materialSearchType: curTab.value,
    purchaseType: formData.value.purchaseType,
  };

  addOrEditMaterialVisible.value = true;
}

// 关闭点击
async function closeClick() {
  drawerVisible.value = false;
}

// 获取供应商名称
async function getSupplierContractlList() {
  const params = {
    purchaseType: formData.value.purchaseType as PurchaseTypeEnum,
  };
  const res = await getSupplierAndContractlList(params);
  contractAndSupplierOptions.value = res;

  supplierOptions.value = res.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
}

async function init() {}

const prevBtn = () => {
  emit('move', -1);
};
const nextBtn = () => {
  emit('move', 1);
};
onBeforeMount(() => {});
</script>

<style lang="scss" scoped>
.el-drawer__header {
  padding: 0 16px;
  margin-bottom: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  // width: 80% !important;/
  min-width: 1200px;
  margin-left: auto;
}
</style>

<style>
.pointer-events-none {
  z-index: 210 !important;
}
.el-drawer__body {
  padding: 0;
}
</style>
