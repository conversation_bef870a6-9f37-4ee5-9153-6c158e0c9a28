<template>
  <div class="goodslist h-full">
    <vxe-grid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
      <template #top></template>
      <template #seq="{ row, $rowIndex }">
        <div v-if="row.id">{{ $rowIndex }}</div>
      </template>
      <template #name="{ row, $rowIndex }">
        <div class="flex items-center justify-center">
          <div class="flex">
            <div>{{ row.name }}</div>
            <div
              v-if="
                ($rowIndex === 1 && tableOptions.data.length <= 0) ||
                $rowIndex === tableOptions.data.length - 1
              "
            >
              <ElButton size="small" @click="transferDataClick({ row })">
                +
              </ElButton>
            </div>
          </div>
        </div>
      </template>
      <template #alterType="{ row }">
        <div v-if="row.sourceMaterialContractId && row.id">
          {{ getAlterEnumLabel(row.changeType) }}
        </div>
        <div v-else-if="row.id">{{ '增项' }}</div>
      </template>
    </vxe-grid>

    <UnitConversion
      ref="unitEl"
      :visible="unitConverSionVisible"
      :options="unitConverSionOption"
      @add-unit="addUnit"
      @edit-unit="editUnit"
      @del-unit="delUnit"
      @refresh="refreshUnitData"
      @insert="insertUnitRow"
    />

    <AddOrEditMaterial
      v-model:visible="transferSelectorVisible"
      :contract-info-data="contractInfo"
      title="选择材料"
      @refresh="refreshData"
    />
  </div>
</template>
<script lang="ts" setup>
import type { getContractMaterialDetailType } from '#/api/enterpriseCenter/materialManagement/materialContract';

import {
  inject,
  nextTick,
  onBeforeMount,
  provide,
  reactive,
  ref,
  watch,
} from 'vue';

import Big from 'big.js';
import { ElButton, ElMessage, ElMessageBox } from 'element-plus';

import {
  addContractMaterialUnit,
  delContractMaterialDetail,
  delContractMaterialUnit,
  editContractMaterialDetail,
  editContractMaterialUnit,
  getContractMaterialDetail,
  getContractMaterialUnit,
  moveContractMaterial,
} from '#/api/enterpriseCenter/materialManagement/materialContract';
import UnitConversion from '#/components/UnitConversion/index.vue';
import { amountFormat } from '#/utils/vxeTool';

import AddOrEditMaterial from '../AddOrEditMaterial.vue';

defineOptions({
  name: 'GoodsList',
});
const props = withDefaults(
  defineProps<{
    contractInfoData: any;
  }>(),
  {
    contractInfoData: {
      parentId: null,
      contractId: '',
      contractTemplateType: '',
    },
  },
);
// 合同类型
const ContractTemplateEnum = {
  GENERAL: 'GENERAL', // 通用
  SUBPACKAGE_LABOUR_SERVICE: 'SUBPACKAGE_LABOUR_SERVICE', // 分包-劳务
  SUBPACKAGE_LABOUR_SPECIALTY: 'SUBPACKAGE_LABOUR_SPECIALTY', // 分包-专业

  MATERIALS_PURCHASING: 'MATERIALS_PURCHASING', // 材料-物资采购
  MATERIALS_COMMERCIAL_CONCRETE: 'MATERIALS_COMMERCIAL_CONCRETE', // 材料-商品混凝土
  MATERIALS_LEASING_TURNOVER: 'MATERIALS_LEASING_TURNOVER', // 材料-租赁周转材料
  MACHINERY: 'MACHINERY', // 机械
  OTHERS: 'OTHERS', // 其他
} as const;
// 变更类型
const AlterEnum = {
  ADJUSTMENT_QUANTITY: 'ADJUSTMENT_QUANTITY', // 调量
  ADJUSTMENT_PRICE: 'ADJUSTMENT_PRICE', // 调价
  ADJUSTMENT_QUANTITY_AND_PRICE: 'ADJUSTMENT_QUANTITY_AND_PRICE', // 调量价
  ADD_ITEM: 'ADD_ITEM', // 增项
} as const;
type AlterEnumType = (typeof AlterEnum)[keyof typeof AlterEnum];

function getAlterEnumLabel(status: AlterEnumType) {
  const map = {
    [AlterEnum.ADJUSTMENT_QUANTITY]: '调量',
    [AlterEnum.ADJUSTMENT_PRICE]: '调价',
    [AlterEnum.ADJUSTMENT_QUANTITY_AND_PRICE]: '调量价',
    [AlterEnum.ADD_ITEM]: '增项',
  };
  return map[status] || '';
}

const editable: any = inject('editable');

const goodList = ref([]);
provide('goodList', goodList);

// 合同的表单
const contractInfo = ref(props.contractInfoData);
watch(
  () => props.contractInfoData,
  (nval) => {
    contractInfo.value = nval;
  },
);
// 新增材料的表单
const addOrEditMaterialForm = ref();
// 单位换算是否展示
const unitEl = ref();
const currentUnit = ref();
const unitConverSionVisible = ref(true);
const unitConverSionOption = ref({
  defaultUnit: null,
  editable: true,
  data: [],
});
const unitOptions = ref([]);
const selectedUnit = ref([]);
// 当前选择的数据
const currentItem = ref();
// 穿梭款是否展示
const transferSelectorVisible = ref(false);
// 表格数据
const tableRef = ref();
let taxRate: null | number = null;

// standard 和 patch 为互斥的列
// const amountFormat = ({ cellValue }: any) => {
//   if (!cellValue) return cellValue;
//   const num = Number(cellValue);
//   return num.toFixed(2);
// };
// 物资采购合同表格配置
const wzColumns = [
  {
    file: 'seq',
    title: '',
    width: '100',
    slots: {
      default: 'seq',
    },
  },
  {
    _type: 'patch',
    field: 'alterType',
    minWidth: '100',
    title: '类型',
    slots: {
      default: 'alterType',
    },
  },
  {
    field: 'code',
    minWidth: '100',
    title: '材料编码',
  },
  {
    field: 'name',
    title: '材料名称',
    minWidth: '160',
    slots: {
      default: 'name',
    },
  },
  {
    field: 'specificationModel',
    title: '规格型号',
    minWidth: '100',
  },
  {
    field: 'qualityStandard',
    title: '材质、性能参数等(或执行的技术质量标准)',
    minWidth: '100',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入材质、性能参数等(或执行的技术质量标准)',
      },
    },
  },
  {
    field: 'brand',
    title: '品牌或厂家',
    width: '100',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入品牌或厂家',
      },
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '80',
    editRender: {
      name: 'VxeSelect',
      options: selectedUnit,
    },
  },
  // patch
  {
    _type: 'patch',
    title: '原合同单价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税单价',
        minWidth: '80',
        field: 'priceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税额',
        minWidth: '80',
        field: 'addedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税单价',
        minWidth: '80',
        field: 'priceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  // patch
  {
    _type: 'patch',
    title: '变更后合同单价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税单价',
        minWidth: '80',
        field: 'changePriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税额',
        minWidth: '80',
        field: 'changeAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税单价',
        minWidth: '80',
        field: 'changePriceIncludingTax',
        headerAlign: 'center',
        editRender: {
          name: 'VxeNumberInput',
          props: {
            placeholder: '请输入含税单价',
            type: 'float',
            digits: 100,
            autoFill: false,
          },
        },
      },
      {
        title: '变更增减',
        minWidth: '80',
        field: 'priceCalculate',
        headerAlign: 'center',
        cellRender: {
          showNegativeStatus: true,
        },
      },
    ],
  },
  // standard
  {
    _type: 'standard',
    title: '单价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税单价',
        minWidth: '80',
        field: 'changePriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税额',
        minWidth: '80',
        field: 'changeAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税单价',
        minWidth: '80',
        field: 'changePriceIncludingTax',
        headerAlign: 'center',
        editRender: {
          name: 'VxeNumberInput',
          props: {
            placeholder: '请输入含税单价',
            type: 'float',
            digits: 100,
            autoFill: false,
          },
        },
      },
    ],
  },
  // standard
  {
    _type: 'standard',
    field: 'changeQuantity',
    title: '暂定数量',
    width: '100',
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入暂定数量',
        type: 'float',
        digits: 100,
        autoFill: false,
      },
    },
  },

  // patch
  {
    _type: 'patch',
    title: '暂定数量',
    headerAlign: 'center',
    children: [
      {
        title: '原合同',
        minWidth: '80',
        field: 'provisionalQuantity',
        headerAlign: 'center',
      },
      {
        title: '已结算',
        minWidth: '80',
        field: 'settlementQuantity',
        headerAlign: 'center',
      },
      {
        title: '变更后',
        minWidth: '80',
        field: 'changeQuantity',
        headerAlign: 'center',
        editRender: {
          name: 'VxeNumberInput',
          props: {
            placeholder: '请输入暂定数量',
            type: 'float',
            digits: 100,
            autoFill: false,
          },
        },
      },
      {
        title: '变更增减',
        minWidth: '80',
        field: 'quantityCalculate',
        headerAlign: 'center',
        cellRender: {
          showNegativeStatus: true,
        },
      },
    ],
  },

  // patch
  {
    _type: 'patch',
    title: '变更前暂定总价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税总价',
        minWidth: '80',
        field: 'totalPriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税总额',
        minWidth: '80',
        field: 'totalValueAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税总价',
        minWidth: '80',
        field: 'totalPriceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  // patch
  {
    _type: 'patch',
    title: '变更后暂定总价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税总价',
        minWidth: '80',
        field: 'changeTotalPriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税总额',
        minWidth: '80',
        field: 'changeTotalValueAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税总价',
        minWidth: '80',
        field: 'changeTotalPriceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  // patch
  {
    _type: 'patch',
    title: '变更增减金额(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税总价',
        minWidth: '80',
        field: 'changeCalculateTotalPriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税总额',
        minWidth: '80',
        field: 'changeCalculateTotalValueAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税总价',
        minWidth: '80',
        field: 'changeCalculateTotalPriceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  // patch
  {
    _type: 'patch',
    field: 'changePercentage',
    title: '变更增减百分率',
    width: '100',
    formatter: ({ cellValue }: any) => {
      if (!cellValue) return cellValue;
      const num = Number(cellValue);
      return `${num.toFixed(2)}%`;
    },
  },
  {
    _type: 'standard',
    title: '暂定总价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税总价',
        minWidth: '80',
        field: 'changeTotalPriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税总额',
        minWidth: '80',
        field: 'changeTotalValueAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税总价',
        minWidth: '80',
        field: 'changeTotalPriceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  {
    field: 'remark',
    title: '备注',
    width: '100',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入品牌或厂家',
      },
    },
  },
];
// 商品混凝土表格配置
const shColumns = [
  {
    file: 'seq',
    title: '',
    width: '100',
    slots: {
      default: 'seq',
    },
  },
  {
    _type: 'patch',
    field: 'alterType',
    minWidth: '100',
    title: '类型',
    slots: {
      default: 'alterType',
    },
  },
  {
    field: 'code',
    minWidth: '100',
    title: '材料编码',
  },
  {
    field: 'name',
    title: '材料名称',
    minWidth: '160',
    slots: {
      default: 'name',
    },
  },
  {
    field: 'specificationModel',
    title: '规格型号',
    minWidth: '100',
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '80',
    editRender: {
      name: 'VxeSelect',
      options: selectedUnit,
    },
  },
  // patch
  {
    _type: 'patch',
    title: '原合同单价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税单价',
        minWidth: '80',
        field: 'priceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税额',
        minWidth: '80',
        field: 'addedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税单价',
        minWidth: '80',
        field: 'priceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  // patch
  {
    _type: 'patch',
    title: '变更后合同单价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税单价',
        minWidth: '80',
        field: 'changePriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税额',
        minWidth: '80',
        field: 'changeAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税单价',
        minWidth: '80',
        field: 'changePriceIncludingTax',
        headerAlign: 'center',
        editRender: {
          name: 'VxeNumberInput',
          props: {
            placeholder: '请输入含税单价',
            type: 'float',
            digits: 100,
            autoFill: false,
          },
        },
      },
      {
        title: '变更增减',
        minWidth: '80',
        field: 'priceCalculate',
        headerAlign: 'center',
        cellRender: {
          showNegativeStatus: true,
        },
      },
    ],
  },
  // standard
  {
    _type: 'standard',
    title: '单价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税单价',
        minWidth: '80',
        field: 'changePriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税额',
        minWidth: '80',
        field: 'changeAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税单价',
        minWidth: '80',
        field: 'changePriceIncludingTax',
        headerAlign: 'center',
        editRender: {
          name: 'VxeNumberInput',
          props: {
            placeholder: '请输入含税单价',
            type: 'float',
            digits: 100,
            autoFill: false,
          },
        },
      },
    ],
  },
  // standard
  {
    _type: 'standard',
    field: 'changeQuantity',
    title: '暂定数量',
    width: '100',
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入暂定数量',
        type: 'float',
        digits: 100,
        autoFill: false,
      },
    },
  },

  // patch
  {
    _type: 'patch',
    title: '暂定数量',
    headerAlign: 'center',
    children: [
      {
        title: '原合同',
        minWidth: '80',
        field: 'provisionalQuantity',
        headerAlign: 'center',
      },
      {
        title: '已结算',
        minWidth: '80',
        field: 'settlementQuantity',
        headerAlign: 'center',
      },
      {
        title: '变更后',
        minWidth: '80',
        field: 'changeQuantity',
        headerAlign: 'center',
        editRender: {
          name: 'VxeNumberInput',
          props: {
            placeholder: '请输入暂定数量',
            type: 'float',
            digits: 100,
            autoFill: false,
          },
        },
      },
      {
        title: '变更增减',
        minWidth: '80',
        field: 'quantityCalculate',
        headerAlign: 'center',
        cellRender: {
          showNegativeStatus: true,
        },
      },
    ],
  },

  // patch
  {
    _type: 'patch',
    title: '变更前暂定总价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税总价',
        minWidth: '80',
        field: 'totalPriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税总额',
        minWidth: '80',
        field: 'totalValueAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税总价',
        minWidth: '80',
        field: 'totalPriceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  // patch
  {
    _type: 'patch',
    title: '变更后暂定总价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税总价',
        minWidth: '80',
        field: 'changeTotalPriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税总额',
        minWidth: '80',
        field: 'changeTotalValueAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税总价',
        minWidth: '80',
        field: 'changeTotalPriceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  // patch
  {
    _type: 'patch',
    title: '变更增减金额(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税总价',
        minWidth: '80',
        field: 'changeCalculateTotalPriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税总额',
        minWidth: '80',
        field: 'changeCalculateTotalValueAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税总价',
        minWidth: '80',
        field: 'changeCalculateTotalPriceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  // patch
  {
    _type: 'patch',
    field: 'changePercentage',
    title: '变更增减百分率',
    width: '100',
    formatter: ({ cellValue }: any) => {
      if (!cellValue) return cellValue;
      const num = Number(cellValue);
      return `${num.toFixed(2)}%`;
    },
  },
  {
    _type: 'standard',
    title: '暂定总价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税总价',
        minWidth: '80',
        field: 'changeTotalPriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税总额',
        minWidth: '80',
        field: 'changeTotalValueAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税总价',
        minWidth: '80',
        field: 'changeTotalPriceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  {
    field: 'remark',
    title: '备注',
    width: '100',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入品牌或厂家',
      },
    },
  },
];
// 租赁周转材料表格配置
const zzColumns = [
  {
    file: 'seq',
    title: '',
    width: '100',
    slots: {
      default: 'seq',
    },
  },
  {
    _type: 'patch',
    field: 'alterType',
    minWidth: '100',
    title: '类型',
    slots: {
      default: 'alterType',
    },
  },
  {
    field: 'code',
    minWidth: '80',
    title: '租赁物资编码',
  },
  {
    field: 'name',
    title: '租赁物资名称',
    minWidth: '160',
    slots: {
      default: 'name',
    },
  },
  {
    field: 'specificationModel',
    minWidth: '80',
    title: '规格型号',
  },
  {
    field: 'unit',
    title: '单位',
    width: '100',
    editRender: {
      name: 'VxeSelect',
      options: selectedUnit,
    },
  },
  // 标准的数据
  {
    _type: 'standard',
    title: '单价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税单价',
        minWidth: '80',
        field: 'changePriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税额',
        minWidth: '80',
        field: 'changeAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税单价',
        minWidth: '80',
        field: 'changePriceIncludingTax',
        headerAlign: 'center',
        editRender: {
          name: 'VxeNumberInput',
          props: {
            placeholder: '请输入含税单价',
            type: 'float',
            digits: 100,
            autoFill: false,
          },
        },
      },
    ],
  },
  // 补充的数据
  {
    _type: 'patch',
    title: '原合同单价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税单价',
        minWidth: '80',
        field: 'priceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税额',
        minWidth: '80',
        field: 'addedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税单价',
        minWidth: '80',
        field: 'priceIncludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
    ],
  },
  // 补充的数据
  {
    _type: 'patch',
    title: '变更后合同单价(元)',
    headerAlign: 'center',
    children: [
      {
        title: '不含税单价',
        minWidth: '80',
        field: 'changePriceExcludingTax',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '增值税额',
        minWidth: '80',
        field: 'changeAddedTaxAmount',
        headerAlign: 'center',
        formatter: amountFormat,
      },
      {
        title: '含税单价',
        minWidth: '80',
        field: 'changePriceIncludingTax',
        headerAlign: 'center',
        editRender: {
          name: 'VxeNumberInput',
          props: {
            placeholder: '请输入含税单价',
            type: 'float',
            digits: 100,
            autoFill: false,
          },
        },
      },
      {
        title: '变更增减',
        minWidth: '80',
        field: 'priceCalculate',
        headerAlign: 'center',
        formatter: amountFormat,
        cellRender: {
          showNegativeStatus: true,
        },
      },
    ],
  },
  // 标准的数据
  {
    _type: 'standard',
    field: 'changeQuantity',
    title: '暂定数量',
    width: '100',
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入暂定数量',
        type: 'float',
        digits: 100,
        autoFill: false,
      },
    },
  },
  // 补充的数据
  {
    _type: 'patch',
    title: '暂定数量',
    headerAlign: 'center',
    children: [
      {
        title: '原合同',
        minWidth: '80',
        field: 'provisionalQuantity',
        headerAlign: 'center',
      },
      {
        title: '变更后',
        minWidth: '80',
        field: 'changeQuantity',
        headerAlign: 'center',
        editRender: {
          name: 'VxeNumberInput',
          props: {
            placeholder: '请输入暂定数量',
            type: 'float',
            digits: 100,
            autoFill: false,
          },
        },
      },
      {
        title: '变更增减',
        minWidth: '80',
        field: 'quantityCalculate',
        headerAlign: 'center',
        cellRender: {
          showNegativeStatus: true,
        },
      },
    ],
  },

  {
    field: 'changeProvisionalDays',
    title: '暂定租赁天数',
    minWidth: '80',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入暂定租赁天数',
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    width: '100',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入品牌或厂家',
      },
    },
  },
];

const tableOptions = reactive<any>({
  size: 'mini',
  height: '94%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  cellClassName: ({ row, column }: any) => {
    return column.editRender || !row.id ? '' : 'bg-gray-100';
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      // 版本数据已启用无法进行编辑
      if (row?.id === '' || !row?.id) {
        ElMessage.warning('请选择有效数据');
        return false;
      }
      if (!taxRate) {
        ElMessage.warning('当前货物清单暂未保存增值税税率,请前往合同文本保存');
        return false;
      }
      if (!editable.value) {
        ElMessage.warning('当前数据已提交,不可编辑');
        return false;
      }
      return true;
    },
  },
  loading: true,
  columnConfig: {
    resizable: true,
  },
  mouseConfig: {
    selected: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'MOVE_UP',
            name: '上移',
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
            disabled: false,
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
            disabled: false,
          },
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options, row, rowIndex }: any) => {
      // 内置节点控制
      if (!row?.id || row.id === '') {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'MOVE_DOWN': {
            item.disabled = rowIndex === tableOptions.data.length - 2;
            break;
          }
          case 'MOVE_UP': {
            item.disabled = rowIndex === 1;
            break;
          }
          // 其他按钮始终可用
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  columns: wzColumns,
  data: [],
});
// 表格事件
const gridEvents = {
  cellClick({ row }: any) {
    currentItem.value = row;
    if (row.id && row.id !== '') {
      currentUnit.value = row.unit;
      getUnitList();
    }
  },
  cellMenu({ row }: any) {
    currentItem.value = row;
    const $grid = tableRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
    }
  },
  async menuClick({ menu, row }: any) {
    const materialContractId = contractInfo.value.contractId;
    const contractTemplateType = contractInfo.value.contractTemplateType;
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delContractMaterialDetail(id, materialContractId);
        if (res) {
          refreshData();
          ElMessage.success('删除成功');
        }
      });
    }
    if (menu.code === 'MOVE_UP') {
      const { id } = row;
      const moveType = 'up';
      const res = await moveContractMaterial({
        id,
        materialContractId,
        contractTemplateType,
        moveType,
      });
      if (res) {
        refreshData();
      }
    }
    if (menu.code === 'MOVE_DOWN') {
      const { id } = row;
      const moveType = 'down';
      const res = await moveContractMaterial({
        id,
        materialContractId,
        contractTemplateType,
        moveType,
      });
      if (res) {
        refreshData();
      }
    }
  },
  async editClosed({ row, column }: any) {
    // 如果修改的是 数量或价格 处理计算数据
    if (
      column.field === 'changePriceIncludingTax' ||
      column.field === 'changeQuantity'
    ) {
      // 不含税单价
      if (row.changePriceIncludingTax && taxRate) {
        const rate = Big(taxRate); // 税率
        row.changePriceExcludingTax = Big(row.changePriceIncludingTax)
          .div(rate.plus(1))
          .round(6)
          .toNumber();
      }
      // 增值税额
      if (row.changePriceIncludingTax && row.changePriceExcludingTax) {
        row.changeAddedTaxAmount = Big(row.changePriceIncludingTax)
          .minus(row.changePriceExcludingTax)
          .round(6)
          .toNumber();
      }
      // 不含税总价
      if (row.changePriceExcludingTax && row.changeQuantity) {
        row.changeTotalPriceExcludingTax = Big(row.changePriceExcludingTax)
          .times(row.changeQuantity)
          .toNumber();
      }
      // 增值税总额
      if (row.changeAddedTaxAmount && row.changeQuantity) {
        row.changeTotalValueAddedTaxAmount = Big(row.changeAddedTaxAmount)
          .times(row.changeQuantity)
          .toNumber();
      }
      // 含税总价
      if (row.changePriceIncludingTax && row.changeQuantity) {
        row.changeTotalPriceIncludingTax = Big(row.changePriceIncludingTax)
          .times(row.changeQuantity)
          .toNumber();
      }
      // 原暂定数量
      if (row.changeQuantity && row.provisionalQuantity) {
        row.quantityCalculate = Big(row.changeQuantity)
          .minus(row.provisionalQuantity)
          .toNumber();
      }
      // 变更增减金额[不含税总价]
      if (row.changeTotalPriceExcludingTax && row.totalPriceExcludingTax) {
        row.changeCalculateTotalPriceExcludingTax = Big(
          row.changeTotalPriceExcludingTax,
        )
          .minus(row.totalPriceExcludingTax)
          .toNumber();
      } else if (!row.sourceMaterialContractId) {
        // 如果是增项的话 额外处理
        row.changeCalculateTotalPriceExcludingTax =
          row.changeTotalPriceExcludingTax;
      }
      // 增值税总额
      if (row.changeTotalValueAddedTaxAmount && row.totalValueAddedTaxAmount) {
        row.changeCalculateTotalValueAddedTaxAmount = Big(
          row.changeTotalValueAddedTaxAmount,
        )
          .minus(row.totalValueAddedTaxAmount)
          .toNumber();
      } else if (!row.sourceMaterialContractId) {
        // 如果是增项的话 额外处理
        row.changeCalculateTotalValueAddedTaxAmount =
          row.changeTotalValueAddedTaxAmount;
      }
      // 含税总价
      if (row.changeTotalPriceIncludingTax && row.totalPriceIncludingTax) {
        row.changeCalculateTotalPriceIncludingTax = Big(
          row.changeTotalPriceIncludingTax,
        )
          .minus(row.totalPriceIncludingTax)
          .toNumber();
      } else if (!row.sourceMaterialContractId) {
        // 如果是增项的话 额外处理
        row.changeCalculateTotalPriceIncludingTax =
          row.changeTotalPriceIncludingTax;
      }
      // 变更价格增减
      if (row.changePriceIncludingTax && row.priceIncludingTax) {
        row.priceCalculate = Big(row.changePriceIncludingTax)
          .minus(row.priceIncludingTax)
          .toNumber();
      }
      // 变更增减百分比率
      if (
        row.changeCalculateTotalPriceExcludingTax &&
        row.totalPriceExcludingTax
      ) {
        row.changePercentage = String(
          new Big(row.changeCalculateTotalPriceExcludingTax)
            .div(row.totalPriceExcludingTax)
            .times(100),
        );
      }
      // 类型
      const isNumSame =
        typeof row.quantityCalculate === 'number' &&
        row.quantityCalculate === 0;
      const isPriceSame =
        typeof row.priceCalculate === 'number' && row.priceCalculate === 0;
      let alterType = null;
      if (!isNumSame && !isPriceSame) {
        alterType = AlterEnum.ADJUSTMENT_QUANTITY_AND_PRICE;
      } else if (!isNumSame) {
        alterType = AlterEnum.ADJUSTMENT_QUANTITY;
      } else if (!isPriceSame) {
        alterType = AlterEnum.ADJUSTMENT_PRICE;
      }
      row.alterType = row.sourceMaterialContractId ? alterType : row.alterType;

      countTotal();
    }

    const {
      changePercentage,
      alterType,
      priceCalculate,
      changeCalculateTotalValueAddedTaxAmount,
      changeCalculateTotalPriceIncludingTax,
      changeCalculateTotalPriceExcludingTax,
      quantityCalculate,
      id,
      unit,
      brand,
      qualityStandard,
      remark,
      changePriceIncludingTax,
      changeProvisionalDays,
      changePriceExcludingTax,
      changeAddedTaxAmount,
      changeQuantity,
      changeTotalPriceExcludingTax,
      changeTotalValueAddedTaxAmount,
      changeTotalPriceIncludingTax,
    } = row;
    const contractTemplateType = contractInfo.value.contractTemplateType;
    const params: any = {
      contractTemplateType, // 合同类型
      unit, // 单位
      changePriceIncludingTax, // 含税单价
      changeQuantity, // 数量
      changePriceExcludingTax, // 不含税单价
      changeAddedTaxAmount, // 增值税额
      remark, // 合同说明
      changeTotalPriceExcludingTax, // 不含税总价
      changeTotalValueAddedTaxAmount, // 增值税总额
      changeTotalPriceIncludingTax, // 含税总价
    };
    // 只有物资采购合同存在
    if (contractTemplateType === ContractTemplateEnum.MATERIALS_PURCHASING) {
      params.qualityStandard = qualityStandard; // 材质、性能参数等（或执行的技术质量标准 (物资采购合同)
      params.brand = brand; // 源合同/品牌或厂家 (物资采购合同)
    }
    // 周转材料存在
    if (
      contractTemplateType === ContractTemplateEnum.MATERIALS_LEASING_TURNOVER
    ) {
      params.changeProvisionalDays = Number(changeProvisionalDays); // 变更后天数
    }

    // 添加补充协议的提交数据
    if (contractInfo.value.parentId) {
      params.alterType = alterType; // 变更类型
      params.priceCalculate = priceCalculate; // 变更价格增减
      params.quantityCalculate = quantityCalculate; // 变更数量增减
      // 物资采购合同补充协议的数据
      if (contractTemplateType === ContractTemplateEnum.MATERIALS_PURCHASING) {
        params.changeCalculateTotalPriceExcludingTax =
          changeCalculateTotalPriceExcludingTax; // 变更增减不含税总价
        params.changeCalculateTotalValueAddedTaxAmount =
          changeCalculateTotalValueAddedTaxAmount; // 变更增减增值税总额
        params.changeCalculateTotalPriceIncludingTax =
          changeCalculateTotalPriceIncludingTax; // 变更增减含税总价
        params.changePercentage = changePercentage; // 增减百分比
      }
      // 混凝土材料合同补充协议的数据
      if (
        contractTemplateType ===
        ContractTemplateEnum.MATERIALS_COMMERCIAL_CONCRETE
      ) {
        params.changeCalculateTotalPriceExcludingTax =
          changeCalculateTotalPriceExcludingTax; // 变更增减不含税总价
        params.changeCalculateTotalValueAddedTaxAmount =
          changeCalculateTotalValueAddedTaxAmount; // 变更增减增值税总额
        params.changeCalculateTotalPriceIncludingTax =
          changeCalculateTotalPriceIncludingTax; // 变更增减含税总价
        params.changePercentage = changePercentage; // 增减百分比
      }
      // // 周转材料合同补充协议的数据
      // if(contractTemplateType === ContractTemplateEnum.MATERIALS_LEASING_TURNOVER){
      // }
    }
    const res = await editContractMaterialDetail(id, params);
    if (res) {
      ElMessage.success('修改成功');
      refreshData();
    }
  },
};
// 计算全部的数据
async function countTotal() {
  const filterData = tableOptions.data.filter((v: any) => v.id && v.id !== '');

  // 变更前数据统计
  let totalPriceExcludingTax = new Big(0);
  let totalValueAddedTaxAmount = new Big(0);
  let totalPriceIncludingTax = new Big(0);
  // 变更后数据统计
  let changeTotalPriceExcludingTax = new Big(0);
  let changeTotalValueAddedTaxAmount = new Big(0);
  let changeTotalPriceIncludingTax = new Big(0);
  // 变更增减金额数据统计
  let changeCalculateTotalPriceExcludingTax = new Big(0);
  let changeCalculateTotalValueAddedTaxAmount = new Big(0);
  let changeCalculateTotalPriceIncludingTax = new Big(0);

  const totalItem = tableOptions.data[0];
  filterData.forEach((v: any) => {
    if (v.totalPriceExcludingTax) {
      totalPriceExcludingTax = totalPriceExcludingTax.plus(
        new Big(v.totalPriceExcludingTax),
      );
    }
    if (v.totalValueAddedTaxAmount) {
      totalValueAddedTaxAmount = totalValueAddedTaxAmount.plus(
        new Big(v.totalValueAddedTaxAmount),
      );
    }
    if (v.totalPriceIncludingTax) {
      totalPriceIncludingTax = totalPriceIncludingTax.plus(
        new Big(v.totalPriceIncludingTax),
      );
    }

    if (v.changeTotalPriceExcludingTax) {
      changeTotalPriceExcludingTax = changeTotalPriceExcludingTax.plus(
        new Big(v.changeTotalPriceExcludingTax),
      );
    }
    if (v.changeTotalValueAddedTaxAmount) {
      changeTotalValueAddedTaxAmount = changeTotalValueAddedTaxAmount.plus(
        new Big(v.changeTotalValueAddedTaxAmount),
      );
    }
    if (v.changeTotalPriceIncludingTax) {
      changeTotalPriceIncludingTax = changeTotalPriceIncludingTax.plus(
        new Big(v.changeTotalPriceIncludingTax),
      );
    }

    if (v.changeCalculateTotalPriceExcludingTax) {
      changeCalculateTotalPriceExcludingTax =
        changeCalculateTotalPriceExcludingTax.plus(
          new Big(v.changeCalculateTotalPriceExcludingTax),
        );
    }
    if (v.changeCalculateTotalValueAddedTaxAmount) {
      changeCalculateTotalValueAddedTaxAmount =
        changeCalculateTotalValueAddedTaxAmount.plus(
          new Big(v.changeCalculateTotalValueAddedTaxAmount),
        );
    }
    if (v.changeCalculateTotalPriceIncludingTax) {
      changeCalculateTotalPriceIncludingTax =
        changeCalculateTotalPriceIncludingTax.plus(
          new Big(v.changeCalculateTotalPriceIncludingTax),
        );
    }
  });

  // 变更前数据统计
  totalItem.totalPriceExcludingTax = totalPriceExcludingTax.round(2).toNumber();
  totalItem.totalValueAddedTaxAmount = totalValueAddedTaxAmount
    .round(2)
    .toNumber();
  totalItem.totalPriceIncludingTax = totalPriceIncludingTax.round(2).toNumber();
  // 变更后数据统计
  totalItem.changeTotalPriceExcludingTax = changeTotalPriceExcludingTax
    .round(2)
    .toNumber();
  totalItem.changeTotalValueAddedTaxAmount = changeTotalValueAddedTaxAmount
    .round(2)
    .toNumber();
  totalItem.changeTotalPriceIncludingTax = changeTotalPriceIncludingTax
    .round(2)
    .toNumber();
  // 变更增减金额数据统计
  totalItem.changeCalculateTotalPriceExcludingTax =
    changeCalculateTotalPriceExcludingTax.round(2).toNumber();
  totalItem.changeCalculateTotalValueAddedTaxAmount =
    changeCalculateTotalValueAddedTaxAmount.round(2).toNumber();
  totalItem.changeCalculateTotalPriceIncludingTax =
    changeCalculateTotalPriceIncludingTax.round(2).toNumber();
  // 变更增减百分率
  if (
    totalItem.totalPriceExcludingTax !== 0 &&
    totalItem.changeCalculateTotalPriceExcludingTax !== 0
  ) {
    totalItem.changePercentage = String(
      new Big(totalItem.changeCalculateTotalPriceExcludingTax)
        .div(totalItem.totalPriceExcludingTax)
        .times(100),
    );
  }
}
// 内置分类数据
const staticGoodsItem = {
  id: '',
  name: '合计',
  remark: '',
  type: '',
  disabled: true,
};
const emptyGoodsItem = {
  id: '',
  name: '',
  editable: false,
  disabled: false,
};
// 获取货物数据
async function getGoodsList() {
  tableOptions.loading = true;

  const contractId = contractInfo.value.contractId;
  const contractTemplateType = contractInfo.value.contractTemplateType;
  const params: getContractMaterialDetailType = {
    contractId,
    contractTemplateType,
  };
  const res = await getContractMaterialDetail(contractId, params);
  const strArr = res.taxRate ? res.taxRate.split('-') : [];
  taxRate = res.taxRate ? percentToDecimal(strArr[1]) : null;

  tableOptions.data =
    res.detailList.length === 0
      ? [emptyGoodsItem]
      : [...res.detailList, emptyGoodsItem];
  tableOptions.data.unshift(staticGoodsItem);
  const filterGoodsList = tableOptions.data.filter((item: any) => {
    return item.id && item.id !== '';
  });
  goodList.value = filterGoodsList.map((item: any) => {
    const {
      code,
      specificationModel,
      name,
      materialDictionaryDetailId,
      unit,
      materialDictionaryCategoryId,
      materialDictionaryVersionId,
    } = item;
    return {
      id: materialDictionaryDetailId,
      code,
      materialDictionaryCategoryId,
      materialDictionaryVersionId,
      meteringUnit: unit,
      name,
      specificationModel,
      selected: true,
      disabled: true,
    };
  });

  tableOptions.loading = false;
}
// 点击穿梭框
async function transferDataClick({ row }: any) {
  if (!editable.value) {
    ElMessage.warning('当前数据已提交,不可编辑');
    return false;
  }
  const materialContractId = contractInfo.value.contractId;
  const materialDictionaryCategoryId = row.id;
  addOrEditMaterialForm.value = {
    materialContractId,
    materialDictionaryCategoryId,
  };
  transferSelectorVisible.value = true;
}
// 初始化
async function init() {
  let tableColumns: any = [];
  // props.contractInfoData.contractTemplateType
  const contractTemplateType = contractInfo.value.contractTemplateType;
  switch (contractTemplateType) {
    case ContractTemplateEnum.MATERIALS_COMMERCIAL_CONCRETE: {
      tableColumns = shColumns;

      break;
    }
    case ContractTemplateEnum.MATERIALS_LEASING_TURNOVER: {
      tableColumns = zzColumns;

      break;
    }
    case ContractTemplateEnum.MATERIALS_PURCHASING: {
      tableColumns = wzColumns;

      break;
    }
    // No default
  }
  tableColumns = contractInfo.value.parentId
    ? tableColumns.filter((v: any) => !v._type || v._type === 'patch')
    : tableColumns.filter((v: any) => !v._type || v._type === 'standard');

  tableOptions.columns = tableColumns;
  await getGoodsList();
  countTotal();
}
// 数据刷新
async function refreshData() {
  await getGoodsList();
  setCurrentRow(currentItem.value.id);
}

// 换算单位
async function insertUnitRow() {
  const materialDetailId = currentItem.value.materialDictionaryDetailId;
  const materialContractId = contractInfo.value.contractId;
  const row = {
    materialDetailId, // 明细id
    materialContractId,
    unit: '',
    factor: 1,
    remark: '',
    isOperation: true,
  };
  unitEl.value.addRow(row);
}
async function addUnit(row: any) {
  const params = {
    materialDetailId: row.materialDetailId, // 明细id
    materialContractId: row.materialContractId,
    unit: row.unit,
    factor: row.factor,
    remark: row.remark,
  };

  const res = await addContractMaterialUnit(params);
  if (res) {
    refreshUnitData();
  }
}
async function editUnit(id: string, row: any) {
  const params = {
    unit: row.unit,
    factor: row.factor,
    remark: row.remark,
  };
  const res = await editContractMaterialUnit(id, params);

  if (res) {
    refreshUnitData();
  }
}
async function delUnit(id: string) {
  const res = await delContractMaterialUnit(id);
  if (res) {
    refreshUnitData();
  }
}

async function getUnitList() {
  const materialContractId = contractInfo.value.contractId;
  const materialDetailId = currentItem.value.materialDictionaryDetailId;
  const params = {
    materialDetailId,
    materialContractId,
  };
  const res = await getContractMaterialUnit(params);
  if (res) {
    const options = res.list.map((item: any) => {
      return {
        label: item.unit,
        value: item.unit,
      };
    });
    // 过滤字典本身的单位
    selectedUnit.value = res.list
      .filter((item: any) => item.isShow) // 过滤出isShow为true的项
      .map((item: any) => ({
        // 转换为指定格式
        label: item.unit,
        value: item.unit,
      }));
    console.log('selectedUnit.value', selectedUnit.value);
    unitOptions.value = options;
    unitConverSionOption.value.defaultUnit = res.dictionaryUnit;
    unitConverSionOption.value.data = res.list;
  }
}
async function refreshUnitData() {
  await getUnitList();
}

// 设置高亮数据
async function setCurrentRow(
  value: string,
  key: string = 'id',
  isExpand: boolean = true,
) {
  const activeRow = tableOptions.data.find(
    (v: any) => Reflect.get(v, key) === value,
  );
  const $grid = tableRef.value;
  nextTick(() => {
    if (activeRow) {
      $grid.setCurrentRow(activeRow);
      currentItem.value = activeRow;
    }
    $grid.setAllTreeExpand(isExpand);
  });
}
// 转换百分比
function percentToDecimal(percent: string): number {
  return Number.parseFloat(percent.replace('%', '')) / 100;
}
onBeforeMount(() => {
  init();
});
</script>

<style lang="scss">
.vxe-cell--tree-node,
.vxe-tree-cell {
  padding-left: 0 !important;
}
</style>
