<template>
  <ElDialog
    align-center
    v-bind="$attrs"
    v-model="dialogVisible"
    :title="props.title"
    :destroy-on-close="false"
    width="85%"
    @close="handleClose"
  >
    <Component
      ref="showComponentRef"
      :is="showComponent"
      v-bind="componentProps"
      @components-event="getComponentData"
    />
    <template #footer>
      <ElButton size="small" @click="handleClose"> 关闭 </ElButton>
      <ElButton type="primary" size="small" @click="submit"> 确定 </ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue';

import { ElButton, ElDialog, ElMessage } from 'element-plus';

import {
  AddSelectAccountData,
  AddSelectBusinessCostAccountData,
} from '#/api/projectCenter/projectSetting/businessCost';
import { DICT_TYPE } from '#/views/projectCenter/projectSetting/businessCost/data';

import BusinessCostSubject from './DictDialogComponents/BusinessCostSubject.vue';
import CostDictionary from './DictDialogComponents/CostDictionary.vue';
import MachineryDictRelease from './DictDialogComponents/MachineryDictRelease.vue';
import MaterialDictionary from './DictDialogComponents/MaterialDictionary.vue';
import TaxRateDictionary from './DictDialogComponents/TaxRateDictionary.vue';

const props = withDefaults(
  defineProps<{
    dictType: string;
    dictTypeId: string;
    title: string;
    visible: boolean;
  }>(),
  {
    visible: false,
    title: '弹框',
    dictTypeId: '',
    dictType: 'BUSINESS_COST_SUBJECT',
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

const componentMap = {
  [DICT_TYPE.BUSINESS_COST_SUBJECT]: BusinessCostSubject,
  [DICT_TYPE.MATERIAL_DICTIONARY]: MaterialDictionary,
  [DICT_TYPE.MECHANICAL_DICTIONARY]: MachineryDictRelease,
  [DICT_TYPE.COST_DICTIONARY]: CostDictionary,
  [DICT_TYPE.TAXRATE_DICTIONARY]: TaxRateDictionary,
};

const componentProps = computed(() => ({
  dictTypeId: props.dictTypeId,
  dictType: props.dictType,
}));

const showComponentRef = ref();

const showComponent = computed(() => {
  return componentMap[props.dictType] || null;
});
function handleClose() {
  emit('update:visible', false);
  emit('refresh');
}

// 组件抛出参数
const componentData = ref({});
function getComponentData(data: any) {
  componentData.value = data;
}
async function submit() {
  const { versionId, categoryIds, details, hasChanged } = componentData.value;
  let res = null;
  // 业务成本科目
  if (props.dictType === DICT_TYPE.BUSINESS_COST_SUBJECT) {
    if (!versionId) return ElMessage.warning('请选择版本！');

    // 如果版本没有变化，直接关闭弹窗，不调用接口
    if (hasChanged === false) {
      ElMessage.success('版本未发生变化，无需更新！');
      handleClose();
      return;
    }

    res = await AddSelectBusinessCostAccountData({
      accountDictionaryId: props.dictTypeId,
      versionId,
    });
  } else if (
    // 材料字典 & 机械字典 & 费用字典 & 税率字典
    props.dictType === DICT_TYPE.MATERIAL_DICTIONARY ||
    props.dictType === DICT_TYPE.MECHANICAL_DICTIONARY ||
    props.dictType === DICT_TYPE.COST_DICTIONARY ||
    props.dictType === DICT_TYPE.TAXRATE_DICTIONARY
  ) {
    if (!showComponentRef.value.validateAll()) return;
    res = await AddSelectAccountData({
      accountDictionaryId: props.dictTypeId,
      versionId,
      categoryIds,
      details,
    });
  }
  if (res) {
    ElMessage.success('操作成功！');
    handleClose();
  }
}
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);
</script>

<style scoped lang="scss"></style>
