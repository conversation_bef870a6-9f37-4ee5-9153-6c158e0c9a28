<template>
  <div class="record-box" v-bind="$attrs">
    <ElDrawer
      v-model="drawerVisible"
      @close="handleDrawerClose"
      @open="openDrawer"
    >
      <template #title>
        <span
          class="text"
          :class="[showSideStatus === 'annex' ? 'annexText' : 'recordText']"
        >
          {{ showSideStatus === 'annex' ? '附件' : '变更记录' }}
        </span>
      </template>
      <!-- 变更记录 -->
      <ChangeRecord
        :records-list="records"
        v-if="showSideStatus === 'record'"
      />
      <!-- 附件 -->
      <div v-else>
        <BaseUpload
          v-model:file-list="fileList"
          @success="addSupplierAnnex"
          @remove="delSupplierAnnex"
        />
      </div>
      <div class="side-box">
        <div class="side-item annex" @click="changeItem('annex')">附件</div>
        <div class="side-item record" @click="changeItem('record')">
          变更记录
        </div>
      </div>
    </ElDrawer>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, ref, watch } from 'vue';

import { ElDrawer, ElMessage } from 'element-plus';

import { getFileCloudUrl } from '#/api/couldApi';
import {
  AddSupplierAccessory,
  ChangeRecords,
  DelSupplierAccessory,
  FileAllSupplier,
} from '#/api/enterpriseCenter/enterpriseStandards/supplier';
import BaseUpload from '#/components/BaseUpload/BaseUpload.vue';

import ChangeRecord from './components/changeRecord.vue';

const props = withDefaults(
  defineProps<{
    code: 'annex' | 'record';
    id: string;
    visible: boolean;
  }>(),
  {
    visible: false,
    code: 'annex',
    id: '',
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'update:code', code: string): void;
  (e: 'update:files', file: any): void;
  (e: 'refresh'): void;
  (e: 'fileClose', file: any): void;
}>();
const fileList = ref();
const showSideStatus = ref(props.code);

function changeItem(code: string) {
  emit('update:code', code);
}

function handleDrawerClose() {
  emit('fileClose', fileList.value);
  emit('update:visible', false);
}

const records = ref([]); // 变更记录
async function openDrawer() {
  records.value = await ChangeRecords(props.id);
  await init();
}

async function addSupplierAnnex(data: {
  fileContentType: string;
  fileExt: string;
  fileKey: string;
  fileName: string;
  fileSize: string;
}) {
  if (props.id) {
    // 正常编辑功能
    const params = {
      ...data,
      supplierDirectoryId: props.id,
    };
    const res = await AddSupplierAccessory(params);
    if (res) {
      ElMessage.success('添加成功');
      getList();
    }
  }
}

async function delSupplierAnnex(data: any) {
  if (props.id) {
    // 删除
    const res = await DelSupplierAccessory(data.id);
    if (res) {
      ElMessage.success('删除成功');
      getList();
    }
  }

  // else {
  //   // 删除
  //   fileList.value = fileList.value.filter(
  //     (item: any) => item.fileKey !== data.fileKey,
  //   );
  // }
}

const drawerVisible = ref(props.visible);
watch(
  () => props.visible,
  (newValue) => {
    drawerVisible.value = newValue;
  },
);
watch(
  () => props.code,
  (newValue) => {
    showSideStatus.value = newValue;
  },
);

async function getList() {
  if (!props.id) {
    return;
  }
  const res = await FileAllSupplier(props.id);
  // 获取地址
  const fileKeys = res.map((v: any) => {
    return v.fileKey;
  });
  const urlData = await getFileCloudUrl(fileKeys);
  const data = res.map((v: any) => {
    return {
      size: Number(v.fileSize),
      name: v.fileName,
      key: v.fileKey,
      url: urlData[v.fileKey],
      ...v,
    };
  });
  fileList.value = data;
}

async function init() {
  getList();
}
</script>

<style scoped lang="scss">
.record-box {
  position: relative;

  .text {
    font-size: 16px;
    font-weight: 500;
  }
  .annexText {
    color: #5ac37d;
  }
  .recordText {
    color: #3f90f8;
  }

  .side-box {
    position: absolute;
    width: 20px;
    left: 0;
    top: 80px;
    display: flex;
    align-items: center;
    flex-direction: column;

    .side-item {
      padding: 5px;
      display: flex;
      align-items: center;
      flex-direction: column;
      color: #ffffff;
      width: 100%;
      text-align: center;
      font-size: 12px;
      &:hover {
        cursor: pointer;
      }
    }
    .annex {
      background-color: #5ac37d;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
    }
    .record {
      background-color: #3f90f8;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
    }
  }
}
</style>
