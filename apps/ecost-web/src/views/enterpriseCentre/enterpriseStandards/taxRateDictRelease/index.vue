<template>
  <ColPage v-bind="colPageProps" content-class="content">
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton circle round type="primary">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <!-- 左侧表格区域 -->
      <div
        :style="{ minWidth: '200px' }"
        v-else
        class="bg-card flex h-full flex-col rounded-lg border p-2"
      >
        <div class="flex-row items-center">
          <div class="flex items-center justify-between">
            <ElButton
              type="primary"
              size="small"
              @click="handleMaintenanceVersion"
            >
              维护版本
            </ElButton>
            <ElSelect
              v-model="currentSelectVersion"
              size="small"
              placeholder="请选择税率字典版本"
              style="width: 200px"
              @change="getClassifyList"
              :fit-input-width="true"
            >
              <ElOption
                v-for="item in versions"
                :key="item.id"
                :value="item.id"
                :label="`${item.name}（${EnableStatusText[item.status as keyof typeof EnableStatusText]}）`"
                :title="item.name"
              />
            </ElSelect>
          </div>
          <div class="mb-2 mt-2 flex items-center justify-between">
            <div>
              <ElButton
                :disabled="isDisableAddClassify"
                type="primary"
                size="small"
                @click="addNewClassify"
              >
                新增分类
              </ElButton>
              <ElButton
                :disabled="isDisableAddClassify"
                type="primary"
                size="small"
                @click="addNewLowerLevelClassify"
              >
                新增下级分类
              </ElButton>
            </div>
            <ElDropdown size="small" @command="handleDropDownItem">
              <ElButton plain type="primary" size="small">
                导入文件
                <IconifyIcon icon="bi:arrow-down" />
              </ElButton>
              <template #dropdown>
                <ElDropdownMenu>
                  <ElDropdownItem command="download"> 模版下载 </ElDropdownItem>
                </ElDropdownMenu>
              </template>
            </ElDropdown>
          </div>
        </div>
        <div class="flex-1 overflow-hidden">
          <vxe-grid
            ref="leftGridRef"
            v-bind="leftGridOptions"
            v-on="leftGridMethod"
          >
            <template #empty>
              <span>
                {{
                  currentSelectVersion
                    ? '没有更多数据了！'
                    : '请选择税率字典版本！'
                }}
              </span>
            </template>
          </vxe-grid>
        </div>
      </div>
    </template>
    <div class="bg-card ml-2 flex h-full flex-col rounded-lg border p-2">
      <div class="mb-2 flex items-center">
        <ElButton
          :disabled="isDisabledAddDetailsBtn"
          type="primary"
          size="small"
          @click="addNewDetails"
        >
          新增明细
        </ElButton>
        <ElInput
          v-model="searchStr"
          class="ml-10"
          size="small"
          placeholder="请输入名称"
          style="width: 250px"
          clearable
          @clear="clearSearch"
          @input="searchDetail"
        >
          <template #prefix>
            <ElIcon class="el-input__icon">
              <Search />
            </ElIcon>
          </template>
        </ElInput>
      </div>
      <div class="flex-1 overflow-hidden">
        <vxe-grid
          ref="rightGridRef"
          v-bind="rightGridOptions"
          v-on="rigtGridMethod"
        >
          <!-- 核算类型  -->
          <template #typeEditor="{ row }">
            <vxe-select v-model="row.type" :options="materialDict" />
          </template>
          <template #type="{ row }">
            {{ materialDict.find((item) => item.value === row.type)?.label }}
          </template>
          <template #executeDate="{ row }">
            {{
              row.executeDate && row.executeDate !== ''
                ? dayjs(row.executeDate).format('YYYY/MM/DD')
                : ''
            }}
          </template>
          <template #taxRate="{ row }">
            <div class="flex items-center justify-center">
              <div class="mr-1">{{ row.taxRate }}</div>
              <ElTooltip placement="top-start" :visible="row.changeLogVisible">
                <template #content>
                  <div v-if="row.changeLogDetail">
                    <div v-if="row.changeLogDetail.length > 0">
                      <div
                        class="flex"
                        v-for="v in row.changeLogDetail"
                        :key="v.id"
                      >
                        <div class="mr-1">
                          {{
                            dayjs(v.updateAt).format('YYYY年MM月DD日HH点mm分')
                          }}
                        </div>
                        <div>{{ v.opreateUserName }} 修改税率</div>
                        <div>由 {{ v.oldValue }} 修改为 {{ v.newValue }}</div>
                      </div>
                    </div>
                    <div v-else class="flex">暂无变更记录</div>
                  </div>
                  <div class="flex" v-else>
                    <ElIcon>
                      <Loading />
                    </ElIcon>
                  </div>
                </template>
                <div
                  class="ml-1 mr-1 mt-1"
                  @mouseenter="getChangeLogData({ row })"
                  @mouseleave="row.changeLogVisible = false"
                >
                  <ElIcon size="14">
                    <InfoFilled />
                  </ElIcon>
                </div>
              </ElTooltip>
            </div>
          </template>
          <!-- <template #taxRateEditor="{ row }">
            <div class="flex justify-center">
              <div class="mr-1">{{ row.taxRate }}</div>
            </div>
          </template> -->
        </vxe-grid>
      </div>
    </div>
    <!-- 新增版本 -->
    <AddVersion
      v-if="versionDialog"
      v-model:visible="versionDialog"
      @refresh="refresh"
    />
  </ColPage>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref } from 'vue';

import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { InfoFilled, Loading, Search } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import {
  ElButton,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElIcon,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElTooltip,
} from 'element-plus';
import _ from 'lodash';

import {
  AddTaxRateDicCategory,
  AddTaxRateDicDetail,
  DeleteTaxRateDicCategory,
  DeleteTaxRateDicDetail,
  ListTaxRateDicCategory,
  ListTaxRateDicDetail,
  ListTaxRateDicVersion,
  MoveTaxRateDicCategory,
  MoveTaxRateDicDetail,
  QueryTaxRateDicDetail,
  QueryTaxrateDictionaryChangeLog,
  UpdateTaxRateDicCategory,
  UpdateTaxRateDicDetail,
} from '#/api/enterpriseCenter/enterpriseStandards/taxRateDictRelease';
import { downloadLocalFile } from '#/utils';

import AddVersion from './components/AddVersion.vue';
import {
  colPageProps,
  EnableStatus,
  EnableStatusText,
  materialDict,
} from './data';

// 版本数据
const currentSelectVersion = ref('');
const versions = ref();
const versionDialog = ref(false); // 维护版本
function handleMaintenanceVersion() {
  versionDialog.value = true;
}
async function getVersionList() {
  // 获取版本数据
  versions.value = await ListTaxRateDicVersion();
}
const curVersionInfo = ref(); // 版本切换
const staticData = {
  // 内置数据
  id: '100',
  name: '全部',
  code: 'All',
  versions: 'All',
  parentId: null,
  remark: '内置节点',
  isActive: true,
  type: 'All',
};

function handleDropDownItem(command: string) {
  if (command === 'download') {
    // TODO 模版下载
    downloadLocalFile('/file/税率字典发布.xlsx', '税率字典发布.xlsx');
  }
}
// 左侧表格
const leftGridRef = ref();
const leftCurrent = ref(); // 左侧选中数据
const leftGridOptions = reactive<any>({
  // 配置数据
  size: 'mini',
  height: '100%',
  autoresize: true,
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  mouseConfig: {
    selected: true,
  },
  columnConfig: {
    resizable: true,
  },
  tooltipConfig: {
    showAll: true,
  },
  rowDragConfig: {
    isCrossDrag: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    beforeEditMethod({ row }: { row: any }) {
      // 版本数据已启用无法进行编辑
      if (
        curVersionInfo.value &&
        curVersionInfo.value.status === EnableStatus.ENABLED
      ) {
        return false;
      }
      if (row.id === '100') {
        return false;
      }
      // 弃用的数据无法编辑
      if (!row.isActive) {
        return false;
      }
      return true;
    },
  },
  rowClassName: ({ row }: any) => {
    // if (row.id && row.id === rightCurrent.value?.taxrateDictionaryCategoryId) {
    //   return 'details-row-click-color';
    // }
    // 禁用数据标识
    if (!row.isActive) {
      return 'details-row-click-disable-color';
    }
    return null;
  },
  editRules: {
    name: [{ required: true, message: '名称不得为空！' }],
    code: [{ required: true, message: '编码不得为空！' }],
  },
  columns: [
    {
      treeNode: true,
      field: 'code',
      title: '编码',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入编码',
        },
      },
    },
    {
      field: 'name',
      title: '名称',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入名称',
        },
      },
      formatter: ({ cellValue, row }) =>
        `${cellValue}${row.isActive ? '' : '（废弃）'}`,
    },
    {
      field: 'remark',
      title: '备注',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入备注',
        },
      },
    },
  ],
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除行',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
          {
            code: 'MOVE_UP',
            name: '上移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          },
          {
            code: 'DISCARD',
            name: '废弃',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-warning-triangle' },
          },
          {
            code: 'ENABLED',
            name: '启用',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-success-circle' },
          },
        ],
      ],
    },
    visibleMethod: ({ options, row }: any) => {
      // 1. 内置节点状态控制

      if (
        row.id === '100' ||
        curVersionInfo.value.status === EnableStatus.ENABLED
      ) {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      // 废弃&启用数据控制
      const isActive = row.isActive;
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DISCARD': {
            item.disabled = !isActive;
            break;
          }
          case 'ENABLED': {
            item.disabled = isActive;
            break;
          }
          case 'MOVE_DOWN': {
            const targetParentId = row.parentId;
            const tableData = leftGridOptions.data.filter(
              (v: any) => v.parentId === targetParentId && v.isActive !== false,
            );
            const targetIdx = tableData.findIndex((v: any) => v.id === row.id);

            item.disabled = !isActive || targetIdx === tableData.length - 1;
            break;
          }
          case 'MOVE_UP': {
            const targetParentId = row.parentId;
            const tableData = leftGridOptions.data.filter(
              (v: any) => v.parentId === targetParentId && v.isActive !== false,
            );
            const targetIdx = tableData.findIndex((v: any) => v.id === row.id);
            item.disabled = !isActive || targetIdx === (targetParentId ? 0 : 1);
            break;
          }
          // 其他按钮始终可用
          default: {
            item.disabled = false;
          }
        }
      });

      return true;
    },
  },
  data: [],
});
const leftGridMethod = {
  async cellClick({ row }: any) {
    leftCurrent.value = row;
    // 获取明细数据
    await getDetailsList(currentSelectVersion.value, row.id);
    rightCurrent.value = {};
    searchStr.value = '';
  },
  cellMenu({ row }: any) {
    const $grid = leftGridRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
      leftCurrent.value = row;
    }
  },
  async menuClick({ menu }: any) {
    const currentRow = leftCurrent.value;
    switch (menu.code) {
      case 'DELETE_ROW': {
        if (currentRow.id) {
          ElMessageBox.confirm('确定删除该数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            // 存在下级分类无法删除
            const isExist = leftGridOptions.data.find(
              (item: any) => item.parentId === currentRow.id,
            );
            if (isExist)
              return ElMessage.warning('当前数据存在下级分类，无法删除！');
            const res = await DeleteTaxRateDicCategory(currentRow.id);
            if (res) {
              ElMessage.success('操作成功！');
            }
            await getClassifyList(currentSelectVersion.value);
          });
        } else {
          // id 不存在临时数据删除
          return (leftGridOptions.data = leftGridOptions.data.filter(
            (item: any) => item.id,
          ));
        }
        nextTick(() => {
          leftGridRef.value.setAllTreeExpand(true);
        });

        break;
      }
      case 'DISCARD': {
        // 废弃
        ElMessageBox.confirm('是否废弃该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateTaxRateDicCategory(currentRow.id, {
            isActive: false,
            name: leftCurrent.value.name,
            code: leftCurrent.value.code,
            parentId: leftCurrent.value.parentId,
            taxrateDictionaryVersionId: leftCurrent.value.versionId,
            // remark: leftCurrent.value.remark,
            // type: leftCurrent.value.type,
          });
          if (res) ElMessage.success('操作成功！');
          await getClassifyList(currentSelectVersion.value);
        });
        break;
      }
      case 'ENABLED': {
        // 启用
        ElMessageBox.confirm('确定启用该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateTaxRateDicCategory(currentRow.id, {
            isActive: true,
            name: leftCurrent.value.name,
            code: leftCurrent.value.code,
            parentId: leftCurrent.value.parentId,
            taxrateDictionaryVersionId: leftCurrent.value.versionId,
            // type: leftCurrent.value.type,
            // remark: leftCurrent.value.remark,
          });
          if (res) ElMessage.success('操作成功！');
          await getClassifyList(currentSelectVersion.value);
        });
        break;
      }
      case 'MOVE_DOWN': {
        await leftGridRef.value.moveRowTo(leftCurrent.value, 1);
        await MoveTaxRateDicCategory(leftCurrent.value.id, 'down');
        await getClassifyList(currentSelectVersion.value);
        break;
      }
      case 'MOVE_UP': {
        await leftGridRef.value.moveRowTo(leftCurrent.value, -1);
        await MoveTaxRateDicCategory(leftCurrent.value.id, 'up');
        await getClassifyList(currentSelectVersion.value);
        break;
      }
    }
  },
  async editClosed({ row }: any) {
    if (leftGridRef.value) {
      const errMsg = await leftGridRef.value.validate(row);
      const isUpdateRow = leftGridRef.value.isUpdateByRow(row);
      if (!errMsg) {
        let res = null;
        if (row.id) {
          // 数据是否发生变化
          if (isUpdateRow) {
            // 编辑
            res = await UpdateTaxRateDicCategory(row.id, {
              name: row.name,
              code: row.code,
              remark: row.remark,
              parentId: row.parentId,
              type: row.type,
              taxrateDictionaryVersionId: row.versionId,
            });
          }
        } else {
          // 新增
          res = await AddTaxRateDicCategory({
            name: row.name,
            code: row.code,
            remark: row.remark,
            parentId: row.parentId,
            type: row.type,
            taxrateDictionaryVersionId: row.versionId,
          });
        }
        if (res) ElMessage.success('操作成功！');
        await getClassifyList(currentSelectVersion.value);
      }
    }
  },
};
async function getClassifyList(val: string) {
  // 获取分类数据
  curVersionInfo.value = versions.value.find((item: any) => item.id === val);
  const arr = await ListTaxRateDicCategory(val);
  leftGridOptions.data = arr || [];
  leftGridOptions.data.unshift(staticData);
  nextTick(() => {
    // 默认选中第一项目
    const row = leftGridOptions.data[0];
    leftGridRef.value.setCurrentRow(row);
    leftGridMethod.cellClick({ row });
    leftGridRef.value.setAllTreeExpand(true);
  });
}
async function addNewClassify() {
  // 新增分类
  if (!currentSelectVersion.value) return ElMessage.warning('请选择业务版本！');
  const errMsg = await leftGridRef.value.validate(true);
  if (errMsg) {
    return ElMessage.warning('请完善表格数据！');
  }
  const newClassify = {
    name: '',
    code: '',
    remark: '',
    parentId: leftCurrent.value.parentId,
    versionId: currentSelectVersion.value,
    isActive: true,
  };
  await nextTick(() => {
    leftGridRef.value &&
      leftGridRef.value.insertNextAt(newClassify, leftCurrent.value, -1);
    leftGridRef.value.validate(leftCurrent.value);
  });
}
async function addNewLowerLevelClassify() {
  // 新增下级分类
  if (!currentSelectVersion.value) return ElMessage.warning('请选择业务版本！');
  // 点击第全部行 return
  if (leftCurrent.value.id === '100')
    return ElMessage.warning('当前节点无法新增下级分类！');
  if (rightGridOptions.data.length > 0)
    return ElMessage.warning('当前节点下存在明细数据，无法新增下级分类！');
  const errMsg = await leftGridRef.value.validate(true);
  if (errMsg) {
    return ElMessage.warning('请完善表格数据！');
  }
  const newLowerLevelClassify = {
    name: '',
    code: '',
    remark: '',
    parentId: leftCurrent.value.id,
    versionId: currentSelectVersion.value,
    isActive: true,
  };
  await nextTick(() => {
    leftGridRef.value &&
      leftGridRef.value.insertChildAt(
        newLowerLevelClassify,
        leftCurrent.value,
        -1,
      );
    leftGridRef.value.validate(leftCurrent.value);
  });
}
const isDisableAddClassify = computed(() => {
  // 新增分类与新增下级分类按钮禁用状态
  // 1.版本已启用按钮禁用
  // 2.未选择分类数据禁用
  // 3.选中的分类数据id为空禁用 - id为空意味着是新增数据
  // 4.分类已被弃用禁用
  return currentSelectVersion.value &&
    curVersionInfo.value.status !== EnableStatus.ENABLED
    ? _.isEmpty(leftCurrent.value) ||
        !leftCurrent.value.id ||
        leftCurrent.value.isActive !== true
    : true;
});

// 右侧表格
const rightGridRef = ref();
const rightCurrent = ref(); // 右侧选中数据
const rightGridOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  autoresize: true,
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  mouseConfig: {
    selected: true,
  },
  rowDragConfig: {
    isCrossDrag: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: false,
    beforeEditMethod({ row }: any) {
      // 版本数据已启用无法进行编辑
      if (
        curVersionInfo.value &&
        curVersionInfo.value.status === EnableStatus.ENABLED
      ) {
        return false;
      }
      // 废弃数据不可编辑
      if (!row.isActive) {
        return false;
      }
      return true;
    },
  },
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  rowClassName: ({ row }: any) => {
    // 禁用数据标识
    if (!row.isActive) {
      return 'details-row-click-disable-color';
    }
    return null;
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除行',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
          {
            code: 'MOVE_UP',
            name: '上移',
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          },
          {
            code: 'DISCARD',
            name: '废弃',
            prefixConfig: { icon: 'vxe-icon-warning-triangle' },
          },
          {
            code: 'ENABLED',
            name: '启用',
            prefixConfig: { icon: 'vxe-icon-success-circle' },
          },
        ],
      ],
    },
    visibleMethod: ({ options, row, rowIndex }: any) => {
      if (curVersionInfo.value.status === EnableStatus.ENABLED) {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }

      const dataLength = rightGridOptions.data.length;
      const isActive = row?.isActive;

      // 特殊处理：只有一条数据，两个移动按钮都禁用
      const isOnlyOne = dataLength === 1;

      const isFirst = rowIndex === 0;
      const isLast = rowIndex === dataLength - 1;

      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DISCARD': {
            item.disabled = !isActive;
            break;
          }
          case 'ENABLED': {
            item.disabled = isActive;
            break;
          }
          case 'MOVE_DOWN': {
            item.disabled = !isActive || isOnlyOne || isLast;
            break;
          }
          case 'MOVE_UP': {
            item.disabled = !isActive || isOnlyOne || isFirst;
            break;
          }
          default: {
            item.disabled = false;
          }
        }
      });

      return true;
    },
  },
  editRules: {
    name: [{ required: true, message: '名称不得为空！' }],
    code: [{ required: true, message: '编码不得为空！' }],
    type: [{ required: true, message: '发票类型不得为空！' }],
    taxRate: [
      { required: true, message: '税率不得为空！' },
      {
        pattern: /^(\d{1,2}%)(,\d{1,2}%)*$/,
        message: '请使用数字加%并用,号隔开',
      },
    ],
  },
  columns: [
    { type: 'seq', width: 50, title: '序号' },
    {
      title: '编码',
      field: 'code',
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入编码',
        },
      },
    },
    {
      width: 150,
      field: 'name',
      title: '名称',
      editRender: {
        name: 'VxeInput',
      },
      formatter: ({ cellValue, row }) =>
        `${cellValue}${row.isActive ? '' : '（废弃）'}`,
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'type',
      title: '发票类型',
      editRender: {},
      slots: {
        edit: 'typeEditor',
        default: 'type',
      },
    },
    {
      field: 'taxRate',
      title: '税率(请用,隔开)',
      showOverflow: false,
      slots: {
        default: 'taxRate',
        // edit: 'taxRateEditor',
      },
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入税率',
        },
      },
    },
    {
      field: 'executeDate',
      title: '执行时间',
      slots: {
        default: 'executeDate',
      },
    },
    {
      field: 'remark',
      title: '备注',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入备注',
        },
      },
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
  ],
  data: [],
});
const rigtGridMethod = {
  // 右侧方法
  async cellClick({ row }: any) {
    rightCurrent.value = row;
    const classifyCurRow = leftGridOptions.data.find(
      (item: any) => item.id === row.taxrateDictionaryCategoryId,
    );
    // 定位 & 高亮
    if (leftGridRef.value && leftGridOptions.data) {
      leftGridRef.value.setCurrentRow(classifyCurRow);
      leftGridRef.value.scrollToRow(classifyCurRow);
    }
  },
  cellMenu({ row }: any) {
    const $grid = rightGridRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
      rightCurrent.value = row;
    }
  },
  async editClosed({ row }: any) {
    if (!rightGridRef.value) return;
    // 如果数据没有通过校验，直接 return
    const errMsg = await rightGridRef.value.validate(row);
    if (errMsg) return;
    if (row.id) {
      if (rightGridRef.value.isUpdateByRow(row)) {
        const res = await UpdateTaxRateDicDetail(row.id, {
          taxrateDictionaryVersionId: row.taxrateDictionaryVersionId, // 版本id
          taxrateDictionaryCategoryId: row.taxrateDictionaryCategoryId, // 分类id
          name: row.name, // 税率字典名称
          code: row.code, // 编码
          type: row.type,
          taxRate: row.taxRate,
          remark: row.remark,
        });
        if (res) ElMessage.success('操作成功！');
      }
    } else {
      const res = await AddTaxRateDicDetail({
        taxrateDictionaryVersionId: row.taxrateDictionaryVersionId, // 版本id
        taxrateDictionaryCategoryId: row.taxrateDictionaryCategoryId, // 分类id
        name: row.name, // 税率字典名称
        code: row.code, // 编码
        type: row.type,
        taxRate: row.taxRate,
        remark: row.remark,
      });
      if (res) ElMessage.success('操作成功！');
    }

    await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
  },
  async menuClick({ menu }: any) {
    const currentRow = rightCurrent.value;
    switch (menu.code) {
      case 'DELETE_ROW': {
        if (currentRow.id) {
          ElMessageBox.confirm('确定删除该数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            const res = await DeleteTaxRateDicDetail(rightCurrent.value.id);
            if (res) ElMessage.success('操作成功！');
            await getDetailsList(
              currentSelectVersion.value,
              leftCurrent.value.id,
            );
          });
        } else {
          // 临时数据删除
          return (rightGridOptions.data = rightGridOptions.data.filter(
            (item: any) => item.id,
          ));
        }
        break;
      }
      case 'DISCARD': {
        // 废弃
        ElMessageBox.confirm('是否废弃该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateTaxRateDicDetail(currentRow.id, {
            isActive: false,
            taxrateDictionaryVersionId:
              rightCurrent.value.taxrateDictionaryVersionId, // 版本id
            taxrateDictionaryCategoryId:
              rightCurrent.value.taxrateDictionaryCategoryId, // 分类id
            code: rightCurrent.value.code, // 编码
            name: rightCurrent.value.name, // 税率字典名称
            taxRate: rightCurrent.value.taxRate,
            type: rightCurrent.value.type,
            remark: rightCurrent.value.remark,
          });
          if (res) ElMessage.success('操作成功！');
          await getDetailsList(
            currentSelectVersion.value,
            leftCurrent.value.id,
          );
        });
        break;
      }
      case 'ENABLED': {
        // 启用
        ElMessageBox.confirm('确定启用该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateTaxRateDicDetail(currentRow.id, {
            isActive: true,
            taxrateDictionaryVersionId:
              rightCurrent.value.taxrateDictionaryVersionId, // 版本id
            taxrateDictionaryCategoryId:
              rightCurrent.value.taxrateDictionaryCategoryId, // 分类id
            code: rightCurrent.value.code, // 编码
            name: rightCurrent.value.name, // 税率字典名称
            taxRate: rightCurrent.value.taxRate,
            type: rightCurrent.value.type,
            remark: rightCurrent.value.remark,
          });
          if (res) ElMessage.success('操作成功！');
          await getDetailsList(
            currentSelectVersion.value,
            leftCurrent.value.id,
          );
        });
        break;
      }
      case 'MOVE_DOWN': {
        await rightGridRef.value.moveRowTo(rightCurrent.value, 1);
        await MoveTaxRateDicDetail(rightCurrent.value.id, 'down');
        await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
        break;
      }
      case 'MOVE_UP': {
        await rightGridRef.value.moveRowTo(rightCurrent.value, -1);
        await MoveTaxRateDicDetail(rightCurrent.value.id, 'up');
        await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
        break;
      }
    }
  },
};
const searchStr = ref(''); // 搜索数据
async function searchDetail() {
  if (!currentSelectVersion.value) {
    return ElMessage.warning('请选择税率字典版本！');
  }

  _.debounce(async () => {
    rightGridOptions.data = await QueryTaxRateDicDetail({
      taxrateDictionaryVersionId: currentSelectVersion.value,
      name: searchStr.value,
    });
  }, 300)();
}
async function clearSearch() {
  await getDetailsList(currentSelectVersion.value, '100');
  rightCurrent.value = {};
  const allRow = leftGridOptions.data.find((item: any) => item.id === '100');
  leftGridRef.value && leftGridRef.value.setCurrentRow(allRow);
}
async function addNewDetails() {
  // 新增明细
  const errMsg = await rightGridRef.value.validate(true);
  if (errMsg) {
    return ElMessage.warning('请完善表格数据！');
  }
  const newDetails = {
    taxrateDictionaryVersionId: currentSelectVersion.value, // 版本id
    taxrateDictionaryCategoryId: leftCurrent.value.id, // 分类id
    code: '', // 编码
    name: '', // 税率字典名称
    type: '',
    taxRate: '',
    remark: '',
    isActive: true,
  };
  if (rightGridRef.value) {
    rightGridRef.value.insertAt(newDetails, -1);
  }
}
async function getChangeLogData({ row }: any) {
  // 获取变更记录数据
  const id = row.id;
  row.changeLogDetail = null;
  const res = await QueryTaxrateDictionaryChangeLog(id);
  row.changeLogDetail = res;

  row.changeLogVisible = true;
}
async function getDetailsList(versionId: string, categoryId: string) {
  // 获取明细数据
  rightGridOptions.data = await ListTaxRateDicDetail({
    taxrateDictionaryVersionId: versionId,
    taxrateDictionaryCategoryId: categoryId,
  });
}
const isDisabledAddDetailsBtn = computed(() => {
  // 新增明细按钮禁用状态
  // 版本启用禁用
  if (
    currentSelectVersion.value &&
    curVersionInfo.value.status === EnableStatus.ENABLED
  ) {
    return true;
  }
  // 如果分类废弃，无法新增明细
  if (!leftCurrent.value?.isActive) {
    return true;
  }
  // 是否叶子节点，叶子节点才可以新增明细
  if (!leftCurrent.value.isLeaf) {
    return true;
  }
  if (_.isEmpty(leftCurrent.value)) {
    return true;
  } else {
    return leftCurrent.value.id ? leftCurrent.value.id === '100' : true;
  }
});

// 刷新接口数据
async function refresh() {
  await getVersionList();
  // 重新获取分类列表
  if (currentSelectVersion.value) {
    await getClassifyList(currentSelectVersion.value);
  }
}

// 初始化
async function init() {
  await getVersionList();
  const activeItem = versions.value.find(
    (v: any) => v.status === EnableStatus.ENABLED,
  );
  if (activeItem) {
    currentSelectVersion.value = activeItem.id;
    await getClassifyList(currentSelectVersion.value);
  }
}

onMounted(async () => {
  init();
});
</script>
<style lang="scss" scoped>
::v-deep(.vxe-grid .vxe-body--row.details-row-click-color) {
  color: #6a6b6e;
  background-color: #f6e692;
}

::v-deep(.vxe-grid .vxe-body--row.details-row-click-disable-color) {
  color: #6a6b6e;
  background-color: #f5f5f5;
}

.content {
  height: 100%;
}

.showBox {
  position: absolute;
  bottom: -40px;
  left: 20px;
  width: 95.5%;

  :deep(.el-card__header) {
    padding: 0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    padding: 6px;
    font-size: 14px;

    .icon {
      font-size: 15px;
      cursor: pointer;
    }
  }
}

.drawer-box {
  position: absolute;
  bottom: 10px;
  left: 20px;
  width: 95.5%;

  :deep(.el-card__body) {
    padding: 10px;
  }

  :deep(.el-card__header) {
    padding: 0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    padding: 6px;
    font-size: 14px;

    .icon {
      font-size: 15px;
      cursor: pointer;
    }
  }
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(100%);
}

.slide-up-enter-active {
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

.slide-up-enter-to {
  opacity: 1;
  transform: translateY(0);
}

.selectPopup {
  z-index: 999999 !important;
  background-color: #f06 !important;
}

:deep(.treeSelect-box) {
  .el-select__wrapper {
    height: 33px !important;

    .el-select__selection {
      height: 25px !important;
    }
  }
}
</style>
